import { useState, useEffect, useRef } from "react";
import { useEnygmaGame } from "../../contexts/EnygmaGameContext";
import { useSpeechOutput } from "../../contexts/SpeechOutputContext";
import "./PlayView.scss";
import IconAura from "../icons/Aura/IconAura";

interface PlayViewProps {
  handleShowLives: () => void;
  handleShowClues: () => void;
  handleExistGame: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowLives,
  handleShowClues,
  handleExistGame,
}) => {
  const { session, askQuestion } = useEnygmaGame();
  const {
    state: { isSpeechPlaying },
  } = useSpeechOutput();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map((msg: SessionMessage): ChatMessage => ({
        id: msg.id,
        text: msg.text,
        sender: msg.sender,
        timestamp: msg.timestamp,
      }));
      setMessages(chatMessages);
    }
  }, [session?.messages]);

   useEffect(() => {
    if (session && (!session.messages || session.messages.length === 0) && messages.length === 0) {
      const sendInitialHola = async () => {
        try {
          setIsLoading(true);
          await askQuestion("Hola");
        } catch (error) {
          console.error("Error sending initial Hola:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialHola();
    }
  }, [session, messages.length, askQuestion]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="content chat-view">
      <div className="menu-left">
        <div className="enygma-logo">
          <img src="assets/game/enygma.png" alt="Enygma" className="enygma-image" />

          <div className={`${isSpeechPlaying ? "speaking" : ""}`}>
            {/* {isSpeechPlaying && ( */}
              <div className="icon-aura speech-indicator">
                <div className="speech-pulse">
                  <IconAura />
                </div>
              </div>
            {/* )} */}
          </div>
        </div>
      </div>

      <div className="game chat-view-wrapper">
        <div className="chat-container">
          <div className="messages-list">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.sender === "user" ? "user-message" : "ai-message"}`}
              >
                <div className="message-content">
                  <span className="message-text">{message.text}</span>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </span>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="message ai-message">
                <div className="message-content">
                  <span className="message-text typing">Enygma está pensando...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="chat-input-container">
          <div className="input-wrapper">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                session?.mode === "player_vs_ia"
                  ? "Responde con 'Sí', 'No', 'Tal vez' o 'No lo sé'..."
                  : "Haz una pregunta sobre el personaje..."
              }
              disabled={isLoading || !session}
              className="chat-input"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim() || isLoading || !session}
              className="send-button"
            >
              {isLoading ? "Enviando..." : "Enviar"}
            </button>
          </div>
        </div>
      </div>

      <div className="menu-right">
        <div onClick={handleShowLives} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/lives.png"
            alt="Vidas"
            className="lives-image"
          />

          {session && (
            <div>{session.questionCount}/{session.maxQuestions}</div>
          )}
        </div>

        <div onClick={handleShowClues} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/clues.png"
            alt="Pistas"
            className="clues-image"
          />
          <div>Pistas</div>
        </div>

        <div onClick={handleExistGame} style={{ cursor: "pointer" }}>
          <img src="assets/game/exit.png" alt="Salir" className="exit-image" />
          <div>Salir</div>
        </div>
      </div>
    </div>
  );
};

export default PlayView;
